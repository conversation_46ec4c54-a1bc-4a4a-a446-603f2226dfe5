package repository

import (
	"context"
	"fmt"
	"time"

	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/chessticize/chessticize-server/internal/repository/common"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// BatchSaveResult contains the results of a batch save operation
type BatchSaveResult struct {
	Imported int     // Number of successfully imported puzzles
	Skipped  int     // Number of skipped puzzles due to errors
	LastID   string  // ID of the last puzzle processed (empty if no puzzles processed)
	Errors   []error // List of errors encountered during batch processing
}

type ILichessPuzzleRepository interface {
	GetByID(ctx context.Context, id string) (*models.LichessPuzzle, error)
	GetByIDs(ctx context.Context, ids []string) (map[string]*models.LichessPuzzle, error)
	List(ctx context.Context, offset, limit int) ([]models.LichessPuzzle, int64, error)
	Save(ctx context.Context, puzzle *models.LichessPuzzle) error
	SaveBatch(ctx context.Context, puzzles []models.LichessPuzzle) (*BatchSaveResult, error)
	UpdateEvaluationsBatch(ctx context.Context, puzzles []models.LichessPuzzle) (*BatchSaveResult, error)
	GetByRatingRange(ctx context.Context, minRating int, maxRating int, themes []string, excludeIDs []string, limit int) ([]models.LichessPuzzle, error)
	GetRandomPuzzles(ctx context.Context, filter common.LichessPuzzleFilter, limit int) ([]models.LichessPuzzle, error)
}

type LichessPuzzleRepository struct {
	db *gorm.DB
}

func NewLichessPuzzleRepository(db *gorm.DB) ILichessPuzzleRepository {
	return &LichessPuzzleRepository{
		db: db,
	}
}

// GetByID retrieves a lichess puzzle by ID
func (r *LichessPuzzleRepository) GetByID(ctx context.Context, id string) (*models.LichessPuzzle, error) {
	var puzzle models.LichessPuzzle
	err := r.db.WithContext(ctx).Where("id = ?", id).First(&puzzle).Error
	if err != nil {
		return nil, err
	}
	return &puzzle, nil
}

// GetByIDs retrieves multiple lichess puzzles by their IDs in a single query
func (r *LichessPuzzleRepository) GetByIDs(ctx context.Context, ids []string) (map[string]*models.LichessPuzzle, error) {
	if len(ids) == 0 {
		return make(map[string]*models.LichessPuzzle), nil
	}

	var puzzles []models.LichessPuzzle
	err := r.db.WithContext(ctx).Where("id IN ?", ids).Find(&puzzles).Error
	if err != nil {
		return nil, err
	}

	// Convert to map for easy lookup
	result := make(map[string]*models.LichessPuzzle, len(puzzles))
	for i := range puzzles {
		result[puzzles[i].ID] = &puzzles[i]
	}

	return result, nil
}

// List retrieves lichess puzzles with pagination
func (r *LichessPuzzleRepository) List(ctx context.Context, offset, limit int) ([]models.LichessPuzzle, int64, error) {
	var puzzles []models.LichessPuzzle
	var totalCount int64

	// Get total count
	if err := r.db.WithContext(ctx).Model(&models.LichessPuzzle{}).Count(&totalCount).Error; err != nil {
		return nil, 0, err
	}

	// Get puzzles with pagination
	err := r.db.WithContext(ctx).
		Offset(offset).
		Limit(limit).
		Order("rating DESC").
		Find(&puzzles).Error

	if err != nil {
		return nil, 0, err
	}

	return puzzles, totalCount, nil
}

// Save saves a lichess puzzle (idempotent - creates or updates)
func (r *LichessPuzzleRepository) Save(ctx context.Context, puzzle *models.LichessPuzzle) error {
	now := time.Now()

	// Set timestamps appropriately
	if puzzle.CreatedAt.IsZero() {
		puzzle.CreatedAt = now
	}
	puzzle.UpdatedAt = now

	return r.db.WithContext(ctx).Save(puzzle).Error
}

// SaveBatch saves multiple lichess puzzles in batches (idempotent - creates or updates)
// This method provides better performance for bulk operations by using GORM's batch processing
func (r *LichessPuzzleRepository) SaveBatch(ctx context.Context, puzzles []models.LichessPuzzle) (*BatchSaveResult, error) {
	if len(puzzles) == 0 {
		return &BatchSaveResult{}, nil
	}

	result := &BatchSaveResult{
		Errors: make([]error, 0),
	}

	now := time.Now()

	// Set timestamps for all puzzles
	for i := range puzzles {
		if puzzles[i].CreatedAt.IsZero() {
			puzzles[i].CreatedAt = now
		}
		puzzles[i].UpdatedAt = now
	}

	// Use GORM's CreateInBatches for better performance
	// Note: GORM's CreateInBatches doesn't handle conflicts (upserts) automatically
	// So we need to handle this manually for idempotency

	// Try to save all puzzles in a single batch first
	// If there are conflicts, we'll fall back to individual saves
	err := r.db.WithContext(ctx).CreateInBatches(puzzles, len(puzzles)).Error

	if err != nil {
		// If batch insert fails (likely due to conflicts), fall back to individual saves
		// This maintains idempotency by using Save which handles upserts
		for i, puzzle := range puzzles {
			if saveErr := r.Save(ctx, &puzzle); saveErr != nil {
				result.Skipped++
				result.Errors = append(result.Errors, saveErr)
			} else {
				result.Imported++
			}

			// Track the last processed puzzle ID
			if i == len(puzzles)-1 {
				result.LastID = puzzle.ID
			}
		}
	} else {
		// Batch insert succeeded - all puzzles were new
		result.Imported = len(puzzles)
		result.LastID = puzzles[len(puzzles)-1].ID
	}

	return result, nil
}

// UpdateEvaluationsBatch updates only the evaluation columns for existing puzzles in batches
// This method verifies that other columns are unchanged and only updates the 3 new evaluation columns
func (r *LichessPuzzleRepository) UpdateEvaluationsBatch(ctx context.Context, puzzles []models.LichessPuzzle) (*BatchSaveResult, error) {
	if len(puzzles) == 0 {
		return &BatchSaveResult{}, nil
	}

	result := &BatchSaveResult{
		Errors: make([]error, 0),
	}

	now := time.Now()

	// Extract puzzle IDs for batch retrieval
	puzzleIDs := make([]string, len(puzzles))
	for i, puzzle := range puzzles {
		puzzleIDs[i] = puzzle.ID
	}

	// Get all existing puzzles in a single query
	existingPuzzles, err := r.GetByIDs(ctx, puzzleIDs)
	if err != nil {
		return nil, fmt.Errorf("failed to retrieve existing puzzles: %w", err)
	}

	// First pass: validate all puzzles and collect valid ones for batch update
	var validPuzzles []models.LichessPuzzle
	for _, puzzle := range puzzles {
		existing, exists := existingPuzzles[puzzle.ID]
		if !exists {
			result.Skipped++
			result.Errors = append(result.Errors, fmt.Errorf("puzzle %s not found", puzzle.ID))
			continue
		}

		// Verify that non-evaluation columns are unchanged
		if !r.verifyPuzzleDataUnchanged(existing, &puzzle) {
			result.Skipped++
			result.Errors = append(result.Errors, fmt.Errorf("puzzle %s has changed non-evaluation data", puzzle.ID))
			continue
		}

		validPuzzles = append(validPuzzles, puzzle)
	}

	// Second pass: perform true batch upsert for all valid puzzles
	if len(validPuzzles) > 0 {
		// Use true batch upsert with GORM's CreateInBatches and OnConflict
		err := r.executeBatchUpdate(r.db.WithContext(ctx), validPuzzles, now)

		if err != nil {
			// If batch upsert fails, fall back to individual updates
			for _, puzzle := range validPuzzles {
				individualResult := r.db.WithContext(ctx).Model(&models.LichessPuzzle{}).
					Where("id = ?", puzzle.ID).
					Updates(map[string]interface{}{
						"best_move_eval":                 puzzle.BestMoveEval,
						"best_move":                      puzzle.BestMove,
						"position_eval_after_first_move": puzzle.PositionEvalAfterFirstMove,
						"updated_at":                     now,
					})

				if individualResult.Error != nil {
					result.Skipped++
					result.Errors = append(result.Errors, fmt.Errorf("failed to update puzzle %s: %w", puzzle.ID, individualResult.Error))
				} else {
					result.Imported++
				}
			}
		} else {
			result.Imported += len(validPuzzles)
		}

		// Track the last processed puzzle ID
		if len(validPuzzles) > 0 {
			result.LastID = validPuzzles[len(validPuzzles)-1].ID
		}
	}

	return result, nil
}

// executeBatchUpdate performs a true batch upsert using GORM's CreateInBatches with OnConflict
func (r *LichessPuzzleRepository) executeBatchUpdate(tx *gorm.DB, puzzles []models.LichessPuzzle, now time.Time) error {
	if len(puzzles) == 0 {
		return nil
	}

	// Set the updated timestamp for all puzzles
	for i := range puzzles {
		puzzles[i].UpdatedAt = now
	}

	// Use GORM's CreateInBatches with OnConflict for true batch upsert
	// This will update only the specified columns when there's a conflict on the primary key
	result := tx.Clauses(clause.OnConflict{
		Columns: []clause.Column{{Name: "id"}}, // Primary key column
		DoUpdates: clause.AssignmentColumns([]string{
			"best_move_eval",
			"best_move",
			"position_eval_after_first_move",
			"updated_at",
		}),
	}).CreateInBatches(&puzzles, len(puzzles))

	if result.Error != nil {
		return fmt.Errorf("failed to batch upsert puzzles: %w", result.Error)
	}

	return nil
}

// verifyPuzzleDataUnchanged checks that all non-evaluation columns are unchanged
func (r *LichessPuzzleRepository) verifyPuzzleDataUnchanged(existing, new *models.LichessPuzzle) bool {
	return existing.FEN == new.FEN &&
		len(existing.Moves) == len(new.Moves) &&
		r.stringArraysEqual(existing.Moves, new.Moves) &&
		existing.Rating == new.Rating &&
		existing.RatingDeviation == new.RatingDeviation &&
		existing.Popularity == new.Popularity &&
		existing.NbPlays == new.NbPlays &&
		len(existing.Themes) == len(new.Themes) &&
		r.stringArraysEqual(existing.Themes, new.Themes) &&
		existing.GameURL == new.GameURL &&
		len(existing.OpeningTags) == len(new.OpeningTags) &&
		r.stringArraysEqual(existing.OpeningTags, new.OpeningTags)
}

// stringArraysEqual compares two string arrays for equality
func (r *LichessPuzzleRepository) stringArraysEqual(a, b []string) bool {
	if len(a) != len(b) {
		return false
	}
	for i := range a {
		if a[i] != b[i] {
			return false
		}
	}
	return true
}

// GetByRatingRange retrieves lichess puzzles within a rating range, optionally filtered by themes and excluding specific IDs
func (r *LichessPuzzleRepository) GetByRatingRange(ctx context.Context, minRating int, maxRating int, themes []string, excludeIDs []string, limit int) ([]models.LichessPuzzle, error) {
	query := r.db.WithContext(ctx).Where("rating >= ? AND rating <= ?", minRating, maxRating)

	// Filter by themes if provided
	if len(themes) > 0 {
		query = query.Where("themes && ?", themes)
	}

	// Exclude specific IDs if provided
	if len(excludeIDs) > 0 {
		query = query.Where("id NOT IN ?", excludeIDs)
	}

	var puzzles []models.LichessPuzzle
	err := query.Order("rating ASC").Limit(limit).Find(&puzzles).Error
	if err != nil {
		return nil, err
	}

	return puzzles, nil
}

// GetRandomPuzzles retrieves random lichess puzzles based on the provided filter
func (r *LichessPuzzleRepository) GetRandomPuzzles(ctx context.Context, filter common.LichessPuzzleFilter, limit int) ([]models.LichessPuzzle, error) {
	query := r.db.WithContext(ctx).Where("rating >= ? AND rating <= ?", filter.MinRating, filter.MaxRating)

	// Filter by themes if provided
	if len(filter.Themes) > 0 {
		query = query.Where("themes && ?", filter.Themes)
	}

	// Exclude specific IDs if provided
	if len(filter.ExcludeIDs) > 0 {
		query = query.Where("id NOT IN ?", filter.ExcludeIDs)
	}

	var puzzles []models.LichessPuzzle
	err := query.Order("RANDOM()").Limit(limit).Find(&puzzles).Error
	if err != nil {
		return nil, err
	}

	return puzzles, nil
}
