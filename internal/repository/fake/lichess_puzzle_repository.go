package fake

import (
	"context"
	"strings"
	"time"

	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/chessticize/chessticize-server/internal/repository"
	"github.com/chessticize/chessticize-server/internal/repository/common"
	"gorm.io/gorm"
)

// LichessPuzzleRepository is a fake implementation of the repository.ILichessPuzzleRepository interface
type LichessPuzzleRepository struct {
	db *gorm.DB
}

// NewLichessPuzzleRepository creates a new fake lichess puzzle repository
func NewLichessPuzzleRepository(db *DB) repository.ILichessPuzzleRepository {
	return &LichessPuzzleRepository{
		db: db.DB,
	}
}

// GetByID retrieves a lichess puzzle by ID
func (r *LichessPuzzleRepository) GetByID(ctx context.Context, id string) (*models.LichessPuzzle, error) {
	var puzzle models.LichessPuzzle
	err := r.db.WithContext(ctx).Where("id = ?", id).First(&puzzle).Error
	if err != nil {
		return nil, err
	}
	return &puzzle, nil
}

// GetByIDs retrieves multiple lichess puzzles by their IDs in a single query
func (r *LichessPuzzleRepository) GetByIDs(ctx context.Context, ids []string) (map[string]*models.LichessPuzzle, error) {
	if len(ids) == 0 {
		return make(map[string]*models.LichessPuzzle), nil
	}

	var puzzles []models.LichessPuzzle
	err := r.db.WithContext(ctx).Where("id IN ?", ids).Find(&puzzles).Error
	if err != nil {
		return nil, err
	}

	// Convert to map for easy lookup
	result := make(map[string]*models.LichessPuzzle, len(puzzles))
	for i := range puzzles {
		result[puzzles[i].ID] = &puzzles[i]
	}

	return result, nil
}

// List retrieves lichess puzzles with pagination
func (r *LichessPuzzleRepository) List(ctx context.Context, offset, limit int) ([]models.LichessPuzzle, int64, error) {
	var puzzles []models.LichessPuzzle
	var totalCount int64

	// Get total count
	if err := r.db.WithContext(ctx).Model(&models.LichessPuzzle{}).Count(&totalCount).Error; err != nil {
		return nil, 0, err
	}

	// Get puzzles with pagination
	err := r.db.WithContext(ctx).
		Offset(offset).
		Limit(limit).
		Order("rating DESC").
		Find(&puzzles).Error

	if err != nil {
		return nil, 0, err
	}

	return puzzles, totalCount, nil
}

// Save saves a lichess puzzle (idempotent - creates or updates)
func (r *LichessPuzzleRepository) Save(ctx context.Context, puzzle *models.LichessPuzzle) error {
	now := time.Now()

	// Set timestamps appropriately
	if puzzle.CreatedAt.IsZero() {
		puzzle.CreatedAt = now
	}
	puzzle.UpdatedAt = now

	return r.db.WithContext(ctx).Save(puzzle).Error
}

// SaveBatch saves multiple lichess puzzles in batches (idempotent - creates or updates)
func (r *LichessPuzzleRepository) SaveBatch(ctx context.Context, puzzles []models.LichessPuzzle) (*repository.BatchSaveResult, error) {
	if len(puzzles) == 0 {
		return &repository.BatchSaveResult{}, nil
	}

	result := &repository.BatchSaveResult{
		Errors: make([]error, 0),
	}

	now := time.Now()

	// Set timestamps for all puzzles
	for i := range puzzles {
		if puzzles[i].CreatedAt.IsZero() {
			puzzles[i].CreatedAt = now
		}
		puzzles[i].UpdatedAt = now
	}

	// Try to save all puzzles in a single batch first
	err := r.db.WithContext(ctx).CreateInBatches(puzzles, len(puzzles)).Error

	if err != nil {
		// If batch insert fails, fall back to individual saves
		for i, puzzle := range puzzles {
			if saveErr := r.Save(ctx, &puzzle); saveErr != nil {
				result.Skipped++
				result.Errors = append(result.Errors, saveErr)
			} else {
				result.Imported++
			}

			// Track the last processed puzzle ID
			if i == len(puzzles)-1 {
				result.LastID = puzzle.ID
			}
		}
	} else {
		// Batch insert succeeded - all puzzles were new
		result.Imported = len(puzzles)
		result.LastID = puzzles[len(puzzles)-1].ID
	}

	return result, nil
}

// UpdateEvaluationsBatch updates only the evaluation columns for existing puzzles in batches
func (r *LichessPuzzleRepository) UpdateEvaluationsBatch(ctx context.Context, puzzles []models.LichessPuzzle) (*repository.BatchSaveResult, error) {
	if len(puzzles) == 0 {
		return &repository.BatchSaveResult{}, nil
	}

	result := &repository.BatchSaveResult{
		Errors: make([]error, 0),
	}

	now := time.Now()

	// For the fake repository, we'll just use the Save method for simplicity
	// In a real implementation, this would verify existing data and update only evaluation columns
	for i, puzzle := range puzzles {
		puzzle.UpdatedAt = now
		if saveErr := r.Save(ctx, &puzzle); saveErr != nil {
			result.Skipped++
			result.Errors = append(result.Errors, saveErr)
		} else {
			result.Imported++
		}

		// Track the last processed puzzle ID
		if i == len(puzzles)-1 {
			result.LastID = puzzle.ID
		}
	}

	return result, nil
}

// GetByRatingRange retrieves lichess puzzles within a rating range, optionally filtered by themes and excluding specific IDs
func (r *LichessPuzzleRepository) GetByRatingRange(ctx context.Context, minRating int, maxRating int, themes []string, excludeIDs []string, limit int) ([]models.LichessPuzzle, error) {
	query := r.db.WithContext(ctx).Where("rating >= ? AND rating <= ?", minRating, maxRating)

	// Filter by themes if provided (SQLite doesn't support array operators, so use LIKE)
	if len(themes) > 0 {
		var themeConditions []string
		var themeValues []interface{}
		for _, theme := range themes {
			themeConditions = append(themeConditions, "themes LIKE ?")
			themeValues = append(themeValues, "%"+theme+"%")
		}
		query = query.Where(strings.Join(themeConditions, " OR "), themeValues...)
	}

	// Exclude specific IDs if provided
	if len(excludeIDs) > 0 {
		query = query.Where("id NOT IN ?", excludeIDs)
	}

	var puzzles []models.LichessPuzzle
	err := query.Order("rating ASC").Limit(limit).Find(&puzzles).Error
	if err != nil {
		return nil, err
	}

	return puzzles, nil
}

// GetRandomPuzzles retrieves random lichess puzzles based on the provided filter
func (r *LichessPuzzleRepository) GetRandomPuzzles(ctx context.Context, filter common.LichessPuzzleFilter, limit int) ([]models.LichessPuzzle, error) {
	query := r.db.WithContext(ctx).Where("rating >= ? AND rating <= ?", filter.MinRating, filter.MaxRating)

	// Filter by themes if provided (SQLite doesn't support array operators, so use LIKE)
	if len(filter.Themes) > 0 {
		var themeConditions []string
		var themeValues []interface{}
		for _, theme := range filter.Themes {
			themeConditions = append(themeConditions, "themes LIKE ?")
			themeValues = append(themeValues, "%"+theme+"%")
		}
		query = query.Where(strings.Join(themeConditions, " OR "), themeValues...)
	}

	// Exclude specific IDs if provided
	if len(filter.ExcludeIDs) > 0 {
		query = query.Where("id NOT IN ?", filter.ExcludeIDs)
	}

	var puzzles []models.LichessPuzzle
	// SQLite uses RANDOM() instead of RANDOM()
	err := query.Order("RANDOM()").Limit(limit).Find(&puzzles).Error
	if err != nil {
		return nil, err
	}

	return puzzles, nil
}
