# Server Configuration
PORT=8080

# Database Configuration
DATABASE_URL=postgres://puzzler:puzzler_secret@localhost:5432/chess_puzzler_dev?sslmode=disable

# JWT Configuration
JWT_SECRET=test-secret-key-for-e2e-testing
JWT_EXPIRY_MINUTES=1440

# Firebase Configuration
FIREBASE_PROJECT_ID=demo-project
FIREBASE_JWKS_ENDPOINT=http://localhost:9099/.well-known/jwks.json
# Set to true to skip token verification (development/testing only)
FIREBASE_SKIP_TOKEN_VERIFICATION=true
