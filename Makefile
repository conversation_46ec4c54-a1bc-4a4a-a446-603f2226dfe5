# Chessticize Server Makefile
#
# Quick Start:
#   make devenv    - Start complete development environment (database + server + puzzles)
#   make stop      - Stop the server
#   make down      - Stop and remove containers
#
.PHONY: build clean short-test test e2e-test up down reset-db start run stop migrate admin-token lint verify devenv populate-puzzles logs

# Include environment variables from .env file
include .env
export

# Build the binary
build:
	mkdir -p build
	go run github.com/99designs/gqlgen generate
	go build -o build/chessticize cmd/chessticize/main.go

# Clean build artifacts
clean:
	rm -rf build

# Run short tests (excluding e2e tests and real database tests)
short-test:
	go test ./... -short

# Run tests (excluding e2e tests)
test:
	go test $(shell go list ./... | grep -v '/e2e')

# Run e2e tests (requires RUN_E2E=true)
e2e-test: stop build start
	RUN_E2E=true go test ./e2e -v

# Start the development environment
up:
	@echo "Setting up development environment..."
	docker compose up -d
	@sleep 3 # Wait for PostgreSQL to be ready

# Stop the development environment
down:
	@echo "Stopping development environment..."
	# Stop containers
	docker compose down

# Reset the database
reset-db: build
	# Delete the volumes to clear the database
	docker compose down -v
	make up migrate

# Start the server asynchronously
start: stop build
	# Start server in background
	./build/chessticize serve &
	@sleep 3 # Wait for server to start

# Run the server synchronously
run: stop build
	./build/chessticize serve

# Stop the server
stop:
	# Stop server
	@pgrep -f "chessticize serve" | xargs -r kill -TERM 2>/dev/null || true
	@sleep 1

# Run migrations
migrate: build
	./build/chessticize migrate

# Generate admin token
admin-token: build
	# Generate admin token with 10-year expiry
	./build/chessticize admin-token -user-id="admin" -email="<EMAIL>" -expiry="87600h"

# Run linter
lint:
	golangci-lint run

# Run formatter
format:
	go fmt ./...

# Run all verifications
verify: format lint build reset-db test e2e-test stop

# Populate Lichess puzzles from CSV
populate-puzzles:
	@echo "Populating Lichess puzzles from testdata..."
	@go run cmd/chessticize/populate-lichess-puzzles/main.go testdata/lichess_db_puzzle_samples.csv 2>/dev/null || echo "Puzzles already exist (skipping duplicates)"

# Start complete development environment (database + server + puzzles)
devenv: up build migrate populate-puzzles
	@echo "Development environment setup complete!"
	@echo "Starting server..."
	@./build/chessticize serve > /tmp/chessticize-server.log 2>&1 &
	@sleep 3 # Wait for server to start
	@if curl -s http://localhost:8080/health > /dev/null 2>&1; then \
		echo ""; \
		echo "🎉 Chessticize development environment is ready!"; \
		echo ""; \
		echo "📊 Server Status:"; \
		echo "  ✅ Database: Running (PostgreSQL on port 5432)"; \
		echo "  ✅ Server: Running on http://localhost:8080"; \
		echo "  ✅ Health Check: http://localhost:8080/health"; \
		echo "  ✅ Lichess Puzzles: Populated from CSV"; \
		echo ""; \
		echo "🔧 Available Endpoints:"; \
		echo "  • REST API: http://localhost:8080/api/v1/"; \
		echo "  • GraphQL: http://localhost:8080/api/v1/graphql/query"; \
		echo "  • Random Puzzles: POST /api/v1/users/me/random-puzzles/lichess"; \
		echo ""; \
		echo "🔑 Generate admin token with: make admin-token"; \
		echo "🛑 Stop environment with: make stop && make down"; \
	else \
		echo "❌ Server failed to start. Check logs with: make logs"; \
		exit 1; \
	fi

# Show server logs
logs:
	@echo "Recent server logs:"
	@tail -f /tmp/chessticize-server.log 2>/dev/null || echo "No log file found. Server may not be running."
